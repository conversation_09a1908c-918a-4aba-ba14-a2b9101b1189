<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class Pasien_new extends Model
{
    protected $table = 'pasien';

    public static function getPatientData($nomr, $findData = [])
    {
        $query = DB::table('master.pasien as p')
            ->select(
                'p.NORM as nomr',
                'p.NAMA as nama_pasien',
                'p.TANGGAL_LAHIR as tanggal_lahir',
                'p.jenis_kelamin',
                'kp.NOMOR as no_whatsapp',
                'kip.NOMOR as nik',
                'rp.INPUT as input_perjanjian',
                'rp.ID as id_perjanjian',
                'rp.TANGGAL as tanggal_perjanjian',
                'rp.STATUS as status_perjanjian',
                'rj.AWAL as jam_awal',
                'rj.AKHIR as jam_akhir',
                'rjd.AWAL as slot_awal',
                'rjd.AKHIR as slot_akhir',
                DB::raw('master.getNamaLengkapPegawai(md.NIP) as nama_dokter'),
                'ref.DESKRIPSI as spesialisasi',
                'mru.DESKRIPSI as nama_ruangan',
                'mr.DESKRIPSI as nama_asuransi',
                'fup.file_ktp',
                'fup.file_rujukan_bpjs',
                'fup.hasil_lab1',
                'fup.hasil_lab2',
                'fup.hasil_lab3'
            )
            ->leftJoin('master.kontak_pasien as kp', 'kp.NORM', '=', 'p.NORM')
            ->leftJoin('master.kartu_identitas_pasien as kip', function ($join) {
                $join->on('kip.NORM', '=', 'p.NORM')
                    ->where('kip.JENIS', '=', 1);
            })
            ->leftJoin('remun_medis.perjanjian as rp', function ($join) {
                $join->on('rp.NOMR', '=', 'p.NORM')
                    ->where('rp.STATUS', '=', 1)
                    ->where('rp.TANGGAL', '>=', date('Y-m-d'));
            })
            ->leftJoin('remun_medis.jadwal as rj', function ($join) {
                $join->on('rj.DOKTER', '=', 'rp.ID_DOKTER')
                    ->on('rj.TANGGAL', '=', 'rp.TANGGAL')
                    ->on('rj.RUANGAN', '=', 'rp.ID_RUANGAN');
            })
            ->leftJoin('master.dokter as md', 'md.ID', '=', 'rp.ID_DOKTER')
            ->leftJoin('master.pegawai as mp', 'md.NIP', '=', 'mp.NIP')
            ->leftJoin('master.referensi as ref', function ($join) {
                $join->on('mp.SMF', '=', 'ref.ID')
                    ->where('ref.jenis', '=', 26);
            })
            ->leftJoin('master.ruangan as mru', function ($join) {
                $join->on('mru.ID', '=', 'rp.ID_RUANGAN')
                    ->where('mru.STATUS', '=', 1);
            })
            ->leftJoin('master.referensi as mr', function ($join) {
                $join->on('mr.ID', '=', 'rp.JAMINAN')
                    ->where('mr.jenis', '=', 10);
            })
            ->leftJoin('pendaftaran_online.nomr_pasien as np', 'np.NORM', '=', 'p.NORM')
            ->leftJoin('pendaftaran_online.file_upload as fup', 'fup.ID_PASIEN', '=', 'np.PENDAFTAR')
            ->leftJoin('remun_medis.jadwal_detail as rjd', 'rjd.ID', '=', 'rp.SLOT')
            ->where('p.NORM', $nomr)
            ->orderBy('rp.INPUT', 'DESC')
            ->limit(1);

        if (!empty($findData)) {
            if (!empty($findData['nik'])) {
                $query->where('kip.NOMOR', $findData['nik']);
            }
            if (!empty($findData['no_wa'])) {
                $query->where('kp.NOMOR', $findData['no_wa']);
            }
            if (!empty($findData['tgl_lahir'])) {
                $query->where(DB::raw('DATE(p.TANGGAL_LAHIR)'), $findData['tgl_lahir']);
            }
        }

        return $query->first();
    }

    public static function checkExistingPerjanjian($nomr, $id_dokter, $id_ruangan, $tanggal)
    {
        return DB::table('remun_medis.perjanjian')->where([
            'NOMR' => $nomr,
            'ID_DOKTER' => $id_dokter,
            'ID_RUANGAN' => $id_ruangan,
            'TANGGAL' => $tanggal,
            'STATUS' => 1
        ])->first();
    }

    public static function insertPerjanjian($dataperjanjian)
    {
        DB::beginTransaction();
        try {
            DB::table('remun_medis.perjanjian')->insert($dataperjanjian);
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
