<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PresensiPpds extends Model
{
    protected $connection = 'ppds_db';
    protected $table = 'presensi_ppds';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'user_id',
        'username',
        'nama_lengkap',
        'tanggal',
        'jam_masuk',
        'jam_keluar',
        'foto_masuk',
        'foto_keluar',
        'latitude_masuk',
        'longitude_masuk',
        'latitude_keluar',
        'longitude_keluar',
        'alamat_masuk',
        'alamat_keluar',
        'status',
        'keterangan'
    ];

    protected $casts = [
        'tanggal' => 'date',
        'jam_masuk' => 'datetime',
        'jam_keluar' => 'datetime',
        'latitude_masuk' => 'decimal:8',
        'longitude_masuk' => 'decimal:8',
        'latitude_keluar' => 'decimal:8',
        'longitude_keluar' => 'decimal:8',
    ];

    /**
     * Check if user already has attendance for today
     */
    public static function hasAttendanceToday($userId)
    {
        return self::where('user_id', $userId)
                   ->whereDate('tanggal', Carbon::today())
                   ->exists();
    }

    /**
     * Get today's attendance for user
     */
    public static function getTodayAttendance($userId)
    {
        return self::where('user_id', $userId)
                   ->whereDate('tanggal', Carbon::today())
                   ->first();
    }

    /**
     * Record check-in
     */
    public static function checkIn($data)
    {
        return self::create([
            'user_id' => $data['user_id'],
            'username' => $data['username'],
            'nama_lengkap' => $data['nama_lengkap'],
            'tanggal' => Carbon::today(),
            'jam_masuk' => Carbon::now(),
            'foto_masuk' => $data['foto'],
            'latitude_masuk' => $data['latitude'],
            'longitude_masuk' => $data['longitude'],
            'alamat_masuk' => $data['alamat'] ?? null,
            'status' => 'masuk'
        ]);
    }

    /**
     * Record check-out
     */
    public function checkOut($data)
    {
        return $this->update([
            'jam_keluar' => Carbon::now(),
            'foto_keluar' => $data['foto'],
            'latitude_keluar' => $data['latitude'],
            'longitude_keluar' => $data['longitude'],
            'alamat_keluar' => $data['alamat'] ?? null,
            'status' => 'keluar'
        ]);
    }

    /**
     * Get attendance history for user
     */
    public static function getAttendanceHistory($userId, $limit = 10)
    {
        return self::where('user_id', $userId)
                   ->orderBy('tanggal', 'desc')
                   ->limit($limit)
                   ->get();
    }
}
