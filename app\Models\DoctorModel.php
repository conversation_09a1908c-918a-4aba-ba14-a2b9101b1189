<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class DoctorModel extends Model
{
    // Jika tabel utama bukan 'doctor_models', bisa didefinisikan:
    // protected $table = 'master.dokter';

    // Jika tidak menggunakan timestamps
    public $timestamps = false;

    public function getDoctor($spesialisasi = '', $isBpjs = false)
    {
        $currentHour = date('H');
        if ((int)$currentHour < 14) {
            $findDate = date('Y-m-d', strtotime('+1 day'));
        } else {
            $findDate = date('Y-m-d', strtotime('+2 days'));
        }

        $query = DB::table('master.dokter as md')
            ->selectRaw("md.id, master.getNamaLengkapPegawai(mp.NIP) as nama_dokter, mr.DESK<PERSON> as ruangan, rmj.KUOTA as kuota, COUNT(rp.ID) as jumlah_perjanjian, mref.DESKRIPSI as spesialisasi")
            ->join('master.pegawai as mp', 'md.NIP', '=', 'mp.NIP')
            ->join('remun_medis.jadwal as rmj', function($join) {
                $join->on('rmj.DOKTER', '=', 'md.ID')
                    ->where('rmj.STATUS', 1);
            })
            ->leftJoin('remun_medis.perjanjian as rp', function($join) {
                $join->on('rp.ID_DOKTER', '=', 'rmj.DOKTER')
                    ->on('rp.TANGGAL', '=', 'rmj.TANGGAL')
                    ->on('rp.ID_RUANGAN', '=', 'rmj.RUANGAN')
                    ->where('rp.RENCANA', 1)
                    ->where('rp.STATUS', 1);
            })
            ->join('master.ruangan as mr', function($join) {
                $join->on('mr.ID', '=', 'rmj.RUANGAN')
                    ->where('mr.STATUS', 1);
            })
            // LEFT JOIN master.referensi selalu dilakukan
            ->leftJoin('master.referensi as mref', function($join) {
                $join->on('mref.ID', '=', 'mp.SMF')
                    ->where('mref.JENIS', 26)
                    ->where('mref.STATUS', 1);
            })
            ->where('rmj.TANGGAL', '>=', $findDate)
            ->where('md.STATUS', 1);

        // Jika spesialisasi diisi, hanya tambahkan where
        if ($spesialisasi) {
            $query->where('mref.ID', $spesialisasi);
        }

        if ($isBpjs == 1) {
            $query->whereIn('rmj.RUANGAN', [
                105020401,
                105020704,
                105020705,
                105020706,
                105020901,
                105060101,
                105110101,
                105120101
            ]);
        } else {
            $query->whereIn('mr.JENIS_KUNJUNGAN', [14, 15]);
        }

        $query->groupBy('md.id', 'mp.NIP', 'mr.DESKRIPSI', 'rmj.KUOTA', 'mref.DESKRIPSI');
        $query->havingRaw('COUNT(rp.ID) <= rmj.KUOTA');
        $query->orderByRaw('master.getNamaLengkapPegawai(mp.NIP) ASC');

        return $query->get()->toArray();
    }
}