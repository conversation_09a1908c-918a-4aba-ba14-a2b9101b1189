# PPDS RSPPU Login & Dashboard System

Sistem login dan dashboard presensi untuk PPDS (Program Pendidikan Dokter Spesialis) RSPPU RS Kanker Dharmais.

## Features

### 🔐 Authentication System
- Login menggunakan database terpisah (dbsdm)
- Session-based authentication
- Middleware protection untuk routes
- Logout dengan clear session

### 📱 Dashboard Features
- Modern responsive UI dengan sidebar navigation
- Real-time attendance status
- Camera access untuk foto presensi
- Geolocation tracking
- Attendance history dengan detail view
- Toast notifications
- Loading states dan error handling

### 📸 Presensi System
- **Check-in/Check-out** dengan foto dan lokasi
- **Photo validation** (max 2MB, format validation)
- **Geolocation tracking** dengan error handling
- **Storage management** untuk foto presensi
- **Detail view** dengan foto dan informasi lengkap

## Database Configuration

### Authentication Database (dbsdm)
```
Host: *************
Database: dbsdm
User: simpeg
Password: 5!Mp36^2024
```

**Table: account**
- KODE (Primary Key)
- PEGAWAI (Foreign Key to pegawai1.NIP)
- USERNAME, PASSWORD
- HAK, STATUS, TANGGAL, OLEH

### PPDS Database (ppds)
```
Host: *************
Database: ppds
User: simpeg
Password: 5!Mp36^2024
```

**Table: presensi_ppds**
- id, user_id, username, nama_lengkap
- tanggal, jam_masuk, jam_keluar
- foto_masuk, foto_keluar
- latitude/longitude masuk/keluar
- alamat_masuk, alamat_keluar
- status, keterangan, timestamps

## Installation & Setup

1. **Clone & Install Dependencies**
```bash
git clone [repository]
cd rsppu
composer install
npm install
```

2. **Environment Configuration**
```bash
cp .env.example .env
# Update database configurations in .env
```

3. **Database Setup**
```bash
php artisan migrate --database=ppds_db
php artisan storage:link
```

4. **Run Development Server**
```bash
php artisan serve --host=0.0.0.0 --port=8000
```

## Usage

### 1. Login
- Akses halaman utama: `http://localhost:8000`
- Klik tombol "Login" di header
- Masukkan username dan password dari database dbsdm
- Sistem akan redirect ke dashboard setelah login berhasil

### 2. Dashboard
- **Status Presensi**: Lihat status presensi hari ini
- **Lokasi**: Sistem otomatis mendeteksi lokasi current
- **Presensi**: Gunakan kamera untuk foto presensi
- **Riwayat**: Lihat history presensi dengan detail

### 3. Presensi Process
1. **Aktifkan Kamera**: Klik "Aktifkan Kamera"
2. **Ambil Foto**: Klik "Ambil Foto" setelah kamera aktif
3. **Preview**: Lihat preview foto yang diambil
4. **Submit**: Klik "Presensi Masuk" atau "Presensi Keluar"
5. **Konfirmasi**: Sistem akan menampilkan notifikasi berhasil

## File Structure

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── AuthWebController.php
│   │   └── PresensiController.php
│   └── Middleware/
│       └── CheckWebAuth.php
├── Models/
│   ├── AccountAuth.php
│   ├── PegawaiAuth.php
│   └── PresensiPpds.php
resources/
├── views/
│   ├── layouts/
│   │   └── dashboard.blade.php
│   ├── dashboard.blade.php
│   └── welcome.blade.php
routes/
└── web.php
storage/
└── app/public/presensi/
```

## API Endpoints

### Authentication
- `POST /login` - User login
- `POST /logout` - User logout
- `GET /check-auth` - Check authentication status

### Dashboard & Presensi
- `GET /dashboard` - Dashboard page
- `POST /presensi/masuk` - Check-in
- `POST /presensi/keluar` - Check-out
- `GET /presensi/status` - Get today's attendance status
- `GET /presensi/detail/{id}` - Get attendance detail

## Security Features

- **CSRF Protection** pada semua form
- **Session-based Authentication** dengan middleware
- **Input Validation** untuk semua request
- **File Validation** untuk upload foto
- **SQL Injection Protection** via Eloquent ORM

## Browser Requirements

- **Camera Access**: Diperlukan untuk fitur presensi
- **Geolocation**: Diperlukan untuk tracking lokasi
- **Modern Browser**: Chrome, Firefox, Safari, Edge (latest versions)
- **HTTPS**: Recommended untuk production (required untuk camera access)

## Troubleshooting

### Camera Issues
- Pastikan browser memiliki permission untuk akses kamera
- Gunakan HTTPS untuk production
- Check browser compatibility

### Location Issues
- Enable location services di browser
- Grant permission untuk akses lokasi
- Check network connectivity

### Database Connection
- Verify database credentials di .env
- Check network connectivity ke database server
- Ensure database exists dan accessible

## Development Notes

- Laravel 8.x framework
- Bootstrap 5 untuk UI components
- MySQL dengan multiple database connections
- Session-based authentication (bukan Laravel Auth)
- Photo storage menggunakan Laravel Storage
- Real-time features dengan JavaScript

## Support

Untuk bantuan teknis atau pertanyaan, silakan hubungi tim development.
