@extends('layouts.dashboard')

@section('title', 'Dashboard PPDS')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-1">Selamat Datang, {{ $user->nama_lengkap }}!</h2>
                            <p class="mb-0">Dashboard Presensi PPDS RSPPU RS Kanker Dharmais</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="text-white-50">
                                <i class="bi bi-calendar-date fs-1"></i>
                            </div>
                            <div class="mt-2">
                                <small>{{ \Carbon\Carbon::now()->format('l, d F Y') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Presensi Hari Ini -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-circle p-3">
                                <i class="bi bi-clock text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">Status Presensi Hari Ini</h6>
                            @if($todayAttendance)
                                @if($todayAttendance->jam_masuk && !$todayAttendance->jam_keluar)
                                    <span class="badge bg-warning">Sudah Masuk</span>
                                    <small class="text-muted d-block">Masuk: {{ $todayAttendance->jam_masuk->format('H:i') }}</small>
                                @elseif($todayAttendance->jam_masuk && $todayAttendance->jam_keluar)
                                    <span class="badge bg-success">Lengkap</span>
                                    <small class="text-muted d-block">
                                        Masuk: {{ $todayAttendance->jam_masuk->format('H:i') }} | 
                                        Keluar: {{ $todayAttendance->jam_keluar->format('H:i') }}
                                    </small>
                                @endif
                            @else
                                <span class="badge bg-danger">Belum Presensi</span>
                                <small class="text-muted d-block">Silakan lakukan presensi masuk</small>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-circle p-3">
                                <i class="bi bi-geo-alt text-white fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">Lokasi Saat Ini</h6>
                            <span id="currentLocation" class="text-muted">Mendeteksi lokasi...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Presensi Actions -->
    <div class="row mb-4" data-section="presensi">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="bi bi-camera me-2"></i>Presensi dengan Kamera
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="text-center">
                                <video id="video" width="100%" height="300" autoplay style="border-radius: 10px; background: #f8f9fa;"></video>
                                <canvas id="canvas" style="display: none;"></canvas>
                                <div class="mt-3">
                                    <button id="startCamera" class="btn btn-outline-primary me-2">
                                        <i class="bi bi-camera-video me-1"></i>Aktifkan Kamera
                                    </button>
                                    <button id="capturePhoto" class="btn btn-primary me-2" disabled>
                                        <i class="bi bi-camera me-1"></i>Ambil Foto
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div id="previewSection" style="display: none;">
                                <h6>Preview Foto:</h6>
                                <img id="photoPreview" class="img-fluid rounded mb-3" style="max-height: 300px;">
                                <div class="d-grid gap-2">
                                    @if(!$todayAttendance)
                                        <button id="checkInBtn" class="btn btn-success btn-lg">
                                            <i class="bi bi-box-arrow-in-right me-2"></i>Presensi Masuk
                                        </button>
                                    @elseif($todayAttendance && !$todayAttendance->jam_keluar)
                                        <button id="checkOutBtn" class="btn btn-warning btn-lg">
                                            <i class="bi bi-box-arrow-left me-2"></i>Presensi Keluar
                                        </button>
                                    @else
                                        <div class="alert alert-success">
                                            <i class="bi bi-check-circle me-2"></i>
                                            Presensi hari ini sudah lengkap
                                        </div>
                                    @endif
                                    <button id="retakePhoto" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Ambil Ulang
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Riwayat Presensi -->
    <div class="row" data-section="history">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>Riwayat Presensi
                    </h5>
                </div>
                <div class="card-body">
                    @if($attendanceHistory->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Tanggal</th>
                                        <th>Jam Masuk</th>
                                        <th>Jam Keluar</th>
                                        <th>Status</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($attendanceHistory as $attendance)
                                    <tr>
                                        <td>{{ $attendance->tanggal->format('d/m/Y') }}</td>
                                        <td>
                                            @if($attendance->jam_masuk)
                                                <span class="badge bg-success">{{ $attendance->jam_masuk->format('H:i') }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attendance->jam_keluar)
                                                <span class="badge bg-warning">{{ $attendance->jam_keluar->format('H:i') }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attendance->jam_masuk && $attendance->jam_keluar)
                                                <span class="badge bg-success">Lengkap</span>
                                            @elseif($attendance->jam_masuk)
                                                <span class="badge bg-warning">Masuk Saja</span>
                                            @else
                                                <span class="badge bg-danger">Tidak Hadir</span>
                                            @endif
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewDetail({{ $attendance->id }})">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="bi bi-inbox fs-1 text-muted"></i>
                            <p class="text-muted mt-2">Belum ada riwayat presensi</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let stream = null;
let currentLocation = null;
let capturedPhoto = null;

// Get current location
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                currentLocation = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                };

                // Reverse geocoding to get address
                fetch(`https://api.opencagedata.com/geocode/v1/json?q=${currentLocation.latitude}+${currentLocation.longitude}&key=YOUR_API_KEY`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.results && data.results.length > 0) {
                            document.getElementById('currentLocation').textContent = data.results[0].formatted;
                        } else {
                            document.getElementById('currentLocation').textContent = `${currentLocation.latitude}, ${currentLocation.longitude}`;
                        }
                    })
                    .catch(() => {
                        document.getElementById('currentLocation').textContent = `${currentLocation.latitude}, ${currentLocation.longitude}`;
                    });
            },
            function(error) {
                document.getElementById('currentLocation').textContent = 'Lokasi tidak dapat dideteksi';
                console.error('Error getting location:', error);
            }
        );
    } else {
        document.getElementById('currentLocation').textContent = 'Geolocation tidak didukung';
    }
}

// Start camera
document.getElementById('startCamera').addEventListener('click', function() {
    const video = document.getElementById('video');

    navigator.mediaDevices.getUserMedia({ video: true })
        .then(function(mediaStream) {
            stream = mediaStream;
            video.srcObject = stream;

            document.getElementById('startCamera').disabled = true;
            document.getElementById('capturePhoto').disabled = false;
        })
        .catch(function(error) {
            console.error('Error accessing camera:', error);
            alert('Tidak dapat mengakses kamera. Pastikan Anda memberikan izin akses kamera.');
        });
});

// Capture photo
document.getElementById('capturePhoto').addEventListener('click', function() {
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const context = canvas.getContext('2d');

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);

    capturedPhoto = canvas.toDataURL('image/jpeg');

    // Show preview
    document.getElementById('photoPreview').src = capturedPhoto;
    document.getElementById('previewSection').style.display = 'block';

    // Stop camera
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
    }

    document.getElementById('startCamera').disabled = false;
    document.getElementById('capturePhoto').disabled = true;
});

// Retake photo
document.getElementById('retakePhoto').addEventListener('click', function() {
    document.getElementById('previewSection').style.display = 'none';
    capturedPhoto = null;
    document.getElementById('startCamera').click();
});

// Check in
document.getElementById('checkInBtn')?.addEventListener('click', function() {
    if (!capturedPhoto) {
        alert('Silakan ambil foto terlebih dahulu');
        return;
    }

    if (!currentLocation) {
        alert('Lokasi belum terdeteksi. Silakan tunggu sebentar.');
        return;
    }

    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();

    const formData = new FormData();
    formData.append('foto', capturedPhoto);
    formData.append('latitude', currentLocation.latitude);
    formData.append('longitude', currentLocation.longitude);
    formData.append('alamat', document.getElementById('currentLocation').textContent);

    fetch('{{ route("presensi.masuk") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        loadingModal.hide();
        console.error('Error:', error);
        alert('Terjadi kesalahan sistem');
    });
});

// Check out
document.getElementById('checkOutBtn')?.addEventListener('click', function() {
    if (!capturedPhoto) {
        alert('Silakan ambil foto terlebih dahulu');
        return;
    }

    if (!currentLocation) {
        alert('Lokasi belum terdeteksi. Silakan tunggu sebentar.');
        return;
    }

    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();

    const formData = new FormData();
    formData.append('foto', capturedPhoto);
    formData.append('latitude', currentLocation.latitude);
    formData.append('longitude', currentLocation.longitude);
    formData.append('alamat', document.getElementById('currentLocation').textContent);

    fetch('{{ route("presensi.keluar") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        loadingModal.hide();
        console.error('Error:', error);
        alert('Terjadi kesalahan sistem');
    });
});

// View detail function
function viewDetail(id) {
    // You can implement this to show detailed attendance info
    alert('Detail presensi ID: ' + id);
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    getCurrentLocation();
});
</script>
@endpush
