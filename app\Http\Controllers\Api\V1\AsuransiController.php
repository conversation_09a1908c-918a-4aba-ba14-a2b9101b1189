<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Referensi;

class AsuransiController extends Controller
{
    public function list(Request $request)
    {
        $id_asuransi = $request->query('id_asuransi');
        $not_in = [1, 2, 4, 40, 826];
        $data = Referensi::getReferensi(10, 1, $id_asuransi, $not_in);

        if (empty($data)) {
            return response()->json([
                'success' => true,
                'message' => 'Data asuransi tidak ditemukan',
                'data' => []
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
}