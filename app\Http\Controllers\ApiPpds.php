<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use App\Models\Presensi;
use App\Traits\LoggingTrait;

class ApiPpds extends Controller
{
    use LoggingTrait;

    public function getPresensi(Request $request)
    {
        // Validasi parameter date wajib ada
        if (!$request->has('date')) {
            return response()->json(["error" => "Parameter 'date' is required"], 400);
        }

        // Ambil parameter date dan konversi ke timestamp
        $date = $request->query('date');
        $start = strtotime($date . ' 00:00:00');
        $finish = strtotime($date . ' 23:59:59');
        // echo $request->query('pagesize');
        // echo "$start and $finish";
        // exit(); 
        // Menyiapkan payload
        $payload = [
            "page" => (int) $request->query('page', 1), // Pastikan integer
            "pagesize" => (int) $request->query('pagesize', 50),  // Default 50 jika tidak ada
            "channel_id" => [4],
            "start_time" => $start,
            "end_time" => $finish,
            "faceattr" => [
                "gender" => 999,
                "age" => 999,
                "glass" => 999,
                "mask" => 999
            ],
            "match_type" => 999,
            "pic_data_sign" => 1,
            "name" => "",
            "certificate_no" => "",
        ];

        // print_r($payload);

        // Inisialisasi cURL
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => 'http://192.168.70.8/api/v2/search/facecap',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json'
            ],
            CURLOPT_HTTPAUTH => CURLAUTH_DIGEST,
            CURLOPT_USERPWD => 'develop:Develop123#'
        ]);

        // Eksekusi cURL dan tangkap response
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        // After curl_close($curl)
        self::logApi(null, null, $httpCode >= 200 && $httpCode < 300 ? 'success' : 'error', $httpCode);

        // Tutup koneksi cURL
        curl_close($curl);

        // Return response dalam format JSON
        return response()->json(json_decode($response, true), $httpCode);
    }

    public function getAbsensi(Request $request)
    {
        try {
            // Hardcoded PIN values
            $pins = [5987, 5988, 5989, 5990, 5991, 5992];

            // First query for presensi data
            $presensiQuery = DB::connection('sdm')->table('pegawai1 as pg')
                ->select(
                    'pg.KTP',
                    'pg.NAMA_LENGKAP',
                    'pn.TANGGAL',
                    'pn.DATANG',
                    'pn.PULANG',
                    DB::raw("'KERJA' AS KETERANGAN")
                )
                ->leftJoin('presensi_new as pn', 'pn.PIN', '=', 'pg.ABSEN')
                ->whereIn('pg.ABSEN', $pins);

            // Second query for jadwal_shift data with custom KETERANGAN
            $jadwalQuery = DB::connection('sdm')->table('pegawai1 as pg')
                ->select(
                    'pg.KTP',
                    'pg.NAMA_LENGKAP',
                    'js.TANGGAL',
                    DB::raw("'' AS DATANG"),
                    DB::raw("'' AS PULANG"),
                    DB::raw("CASE 
                        WHEN js.SHIFT = 'SK' THEN 'SAKIT'
                        WHEN js.SHIFT = 'C' THEN 'CUTI'
                        WHEN js.SHIFT = 'DL' THEN 'DINAS LUAR'
                        ELSE js.SHIFT
                    END AS KETERANGAN")
                )
                ->leftJoin('jadwal_shift as js', 'js.ABSEN', '=', 'pg.ABSEN')
                ->whereIn('pg.ABSEN', $pins);

            // Add date filter if provided
            if ($request->has('tanggal')) {
                $date = $request->query('tanggal');
                if (!strtotime($date)) {
                    return response()->json(["error" => "Invalid date format. Use yyyy-mm-dd"], 400);
                }
                $presensiQuery->whereDate('pn.TANGGAL', $date);
                $jadwalQuery->whereDate('js.TANGGAL', $date);
            }

            // Combine both queries with UNION
            $combinedQuery = $presensiQuery->union($jadwalQuery);

            // Execute the query with ordering
            $absensi = DB::connection('sdm')->table(DB::raw("({$combinedQuery->toSql()}) as data_union"))
                ->mergeBindings($combinedQuery)
                ->orderBy('TANGGAL', 'desc')
                ->get();

            // Prepare response data
            $responseData = [
                'success' => true,
                'data' => $absensi
            ];

            // Log successful response
            self::logApi($absensi, null, 'success', 200);

            return response()->json($responseData, 200);
        } catch (\Exception $e) {
            // Log error
            self::logApi(null, $e, 'error', 500);

            return response()->json([
                'success' => false,
                'error' => 'Database connection failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
