<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;

class SDMServiceProvider extends ServiceProvider
{
    public function register()
    {
        $config = [
            'driver' => 'mysql',
            'host' => '*************',
            'database' => 'dbsdm',
            'username' => 'simpeg',
            'password' => '5!Mp36^2024',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ];

        Config::set('database.connections.sdm', $config);
    }
}
