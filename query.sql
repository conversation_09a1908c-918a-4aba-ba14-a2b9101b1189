SELECT
	mpas.NAMA,
	mpas.TANGGAL_LAHIR,
	mpas.JENIS_KELAMIN,
	pp.TANGGAL,
	mr1.DESKRIPSI,
	pp.NOMOR,
	pp.NORM,
	`master`.getNamaLengkapPegawai ( md.NIP ) AS dpjp,
	c.id,
	c.analisis AS diagnosis,
	c.oleh,
	c<PERSON>tan<PERSON>,
	'2' <PERSON> jenis,
	'cppt' AS source 
FROM
	keperawatan.tb_cppt c
	LEFT JOIN pendaftaran.kunjungan pk ON pk.NOMOR = c.nokun
	LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
	LEFT JOIN aplikasi.pengguna ap ON ap.ID = c.oleh
	JOIN `master`.pegawai mp ON mp.NIP = ap.NIP
	LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = pp.NOMOR
	LEFT JOIN `master`.ruangan mr1 ON mr1.ID = tp.RUANGAN
	LEFT JOIN `master`.ruangan mr2 ON mr2.ID = pk.RUANGAN
	LEFT JOIN `master`.dokter md ON md.ID = tp.DOKTER
	JOIN `master`.pasien mpas ON mpas.NORM = pp.NORM 
WHERE
	mp.NIP = '19880209' 
	AND mp.PROFESI = 11 
ORDER BY
	pp.TANGGAL DESC --     AND mr2.JENIS_KUNJUNGAN NOT IN (0, 4, 5, 11)
	
	LIMIT 100;