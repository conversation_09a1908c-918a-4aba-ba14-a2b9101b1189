<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PegawaiAuth extends Model
{
    protected $connection = 'auth_db';
    protected $table = 'pegawai1';
    protected $primaryKey = 'NIP';
    public $timestamps = false;

    protected $fillable = [
        'NIP',
        'NAMA_LENGKAP',
        'KTP',
        'ABSEN'
    ];

    /**
     * Get pegawai by NIP
     */
    public static function findByNip($nip)
    {
        return self::where('NIP', $nip)->first();
    }

    /**
     * Get pegawai name by NIP
     */
    public static function getNamaLengkap($nip)
    {
        $pegawai = self::findByNip($nip);
        return $pegawai ? $pegawai->NAMA_LENGKAP : null;
    }
}
