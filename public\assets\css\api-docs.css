/* API Documentation Styles */
.api-endpoint {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.api-endpoint h4 {
    color: #1977cc;
    margin-bottom: 15px;
    font-weight: 600;
}

.api-endpoint h5 {
    font-size: 16px;
    margin-top: 15px;
    margin-bottom: 10px;
    font-weight: 600;
}

.api-endpoint pre {
    background-color: #272822;
    color: #f8f8f2;
    border-radius: 5px;
    padding: 15px;
    overflow-x: auto;
}

.api-endpoint code {
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
}

.api-endpoint ul {
    padding-left: 20px;
}

.api-endpoint li {
    margin-bottom: 8px;
}

.api-endpoint strong {
    font-weight: 600;
}

.section-header {
    text-align: center;
    padding-bottom: 30px;
}

.section-header h2 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 20px;
    padding-bottom: 20px;
    position: relative;
    color: #2c4964;
}

.section-header h2:after {
    content: "";
    position: absolute;
    display: block;
    width: 50px;
    height: 3px;
    background: #1977cc;
    bottom: 0;
    left: calc(50% - 25px);
}

.section-header p {
    margin-bottom: 0;
    font-size: 14px;
    color: #919191;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 25px 0 rgba(214, 215, 216, 0.6);
    margin-bottom: 20px;
}

.card-body {
    padding: 25px;
}

.card-body h3 {
    font-size: 24px;
    color: #2c4964;
    font-weight: 700;
    margin-bottom: 20px;
}