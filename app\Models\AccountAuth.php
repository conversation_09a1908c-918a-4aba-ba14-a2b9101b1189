<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AccountAuth extends Model
{
    protected $connection = 'auth_db';
    protected $table = 'account';
    protected $primaryKey = 'KODE';
    public $timestamps = false;

    protected $fillable = [
        'KODE',
        'PEGAWAI',
        'USERNAME',
        'PASSWORD',
        'HAK',
        'STATUS',
        'TANGGAL',
        'OLEH'
    ];

    /**
     * Authenticate user by username and password
     */
    public static function authenticate($username, $password)
    {
        $user = self::where('USERNAME', $username)
                    ->where('STATUS', 1)
                    ->first();

        if ($user && $user->PASSWORD === $password) {
            return $user;
        }

        return null;
    }

    /**
     * Get user by username
     */
    public static function findByUsername($username)
    {
        return self::where('USERNAME', $username)
                   ->where('STATUS', 1)
                   ->first();
    }

    /**
     * Get pegawai relation
     */
    public function pegawai()
    {
        return $this->belongsTo(PegawaiAuth::class, 'PEGAWAI', 'NIP');
    }

    /**
     * Get nama lengkap from pegawai
     */
    public function getNamaLengkapAttribute()
    {
        return $this->pegawai ? $this->pegawai->NAMA_LENGKAP : $this->USERNAME;
    }
}
