<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;

class AccountAuth extends Model
{
    protected $connection = 'auth_db';
    protected $table = 'account';
    protected $primaryKey = 'KODE';
    public $timestamps = false;

    protected $fillable = [
        'KODE',
        'PEGAWAI',
        'USERNAME',
        'PASSWORD',
        'HAK',
        'STATUS',
        'TANGGAL',
        'OLEH'
    ];

    /**
     * Authenticate user by username and password
     */
    public static function authenticate($username, $password)
    {
        // Debug: Log the authentication attempt
        Log::info('Authentication attempt', [
            'username' => $username,
            'password_length' => strlen($password)
        ]);

        $user = self::where('USERNAME', $username)->first();

        // Debug: Log user found
        if ($user) {
            Log::info('User found', [
                'username' => $user->USERNAME,
                'status' => $user->STATUS,
                'password_in_db' => $user->PASSWORD,
                'password_input' => $password,
                'password_match' => ($user->PASSWORD === $password)
            ]);
        } else {
            Log::info('User not found', ['username' => $username]);
        }

        // Check if user exists and status is active (STATUS can be string "1" or integer 1)
        if ($user && ($user->STATUS == 1 || $user->STATUS == '1')) {
            // Check if password is hashed (starts with $2y$)
            if (substr($user->PASSWORD, 0, 4) === '$2y$') {
                // Use Hash::check for bcrypt passwords
                if (Hash::check($password, $user->PASSWORD)) {
                    Log::info('Password verified with Hash::check');
                    return $user;
                }
            } else {
                // Plain text password comparison
                if ($user->PASSWORD === $password ||
                    trim($user->PASSWORD) === trim($password) ||
                    $user->PASSWORD == $password) {
                    Log::info('Password verified with plain text comparison');
                    return $user;
                }
            }
        }

        Log::info('Authentication failed');
        return null;
    }

    /**
     * Get user by username
     */
    public static function findByUsername($username)
    {
        return self::where('USERNAME', $username)
                   ->where('STATUS', 1)
                   ->first();
    }

    /**
     * Get pegawai relation
     */
    public function pegawai()
    {
        return $this->belongsTo(PegawaiAuth::class, 'PEGAWAI', 'NIP');
    }

    /**
     * Get nama lengkap from pegawai
     */
    public function getNamaLengkapAttribute()
    {
        return $this->pegawai ? $this->pegawai->NAMA_LENGKAP : $this->USERNAME;
    }
}
