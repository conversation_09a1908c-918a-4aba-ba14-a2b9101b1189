<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DoctorModel;

class DoctorController extends Controller
{
    public function index(Request $request)
    {
        // Ambil parameter opsional dari query string
        $spesialisasi = $request->query('spesialisasi', '');
        $isBpjs = $request->query('isbpjs', null);

        // Validasi isbpjs jika diisi, harus 0 atau 1
        if (!is_null($isBpjs) && !in_array($isBpjs, ['0', '1', 0, 1], true)) {
            return response()->json([
                'success' => false,
                'message' => 'Parameter isbpjs harus 0 atau 1',
                'data' => []
            ], 400);
        }

        // Panggil model
        $doctorModel = new DoctorModel();
        $data = $doctorModel->getDoctor($spesialisasi, $isBpjs);
        if (empty($data)) {
            return response()->json([
                'success' => true,
                'message' => 'Data dokter tidak ditemukan',
                'data' => []
            ]);
        }
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
}