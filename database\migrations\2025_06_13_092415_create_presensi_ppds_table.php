<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePresensiPpdsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('ppds_db')->create('presensi_ppds', function (Blueprint $table) {
            $table->id();
            $table->integer('user_id');
            $table->string('username', 100);
            $table->string('nama_lengkap', 255);
            $table->date('tanggal');
            $table->datetime('jam_masuk')->nullable();
            $table->datetime('jam_keluar')->nullable();
            $table->string('foto_masuk', 255)->nullable();
            $table->string('foto_keluar', 255)->nullable();
            $table->decimal('latitude_masuk', 10, 8)->nullable();
            $table->decimal('longitude_masuk', 11, 8)->nullable();
            $table->decimal('latitude_keluar', 10, 8)->nullable();
            $table->decimal('longitude_keluar', 11, 8)->nullable();
            $table->text('alamat_masuk')->nullable();
            $table->text('alamat_keluar')->nullable();
            $table->enum('status', ['masuk', 'keluar'])->default('masuk');
            $table->text('keterangan')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'tanggal']);
            $table->index('tanggal');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('ppds_db')->dropIfExists('presensi_ppds');
    }
}
