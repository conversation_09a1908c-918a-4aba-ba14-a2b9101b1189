<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class AuthenticateBearerToken
{
    public function handle(Request $request, Closure $next)
    {
        $authorizationHeader = $request->header('Authorization');

        // Periksa apakah header Authorization ada
        if (!$authorizationHeader || !str_starts_with($authorizationHeader, 'Bearer ')) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized. Bearer token not provided.',
            ], 401);
        }

        // Ambil token dari header Authorization
        $token = substr($authorizationHeader, 7);

        // Validasi token dari cache
        if (!Cache::has($token)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized. Invalid or expired token.',
            ], 401);
        }

        return $next($request);
    }
}
