<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Presensi extends Model
{
    protected $connection = 'sdm';
    protected $table = 'presensi_new';
    protected $fillable = ['ID', 'PIN', 'TANGGAL', 'DATANG', 'PULANG', 'ALASAN', 'KETERANGAN'];

    public function pegawai()
    {
        return $this->belongsTo(Pegawai::class, 'PIN', 'ABSEN');
    }
}

class Pegawai extends Model
{
    protected $connection = 'sdm';
    protected $table = 'pegawai1';
    protected $fillable = ['NAMA_LENGKAP', 'KTP', 'ABSEN'];
}