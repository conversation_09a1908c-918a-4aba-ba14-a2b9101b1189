<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Models\Ruangan;

class RuanganController extends Controller
{
    public function index(Request $request)
    {
        $id_ruangan = $request->query('id_ruangan');
        $data = Ruangan::getRuanganPoli($id_ruangan);
        if (empty($data)) {
            return response()->json([
                'success' => true,
                'message' => 'Data ruangan tidak ditemukan',
                'data' => []
            ]);
        }
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
}