<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AccountAuth;
use Illuminate\Support\Facades\Session;

class AuthWebController extends Controller
{
    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string'
        ]);

        $username = $request->input('username');
        $password = $request->input('password');

        // Authenticate user
        $user = AccountAuth::authenticate($username, $password);

        if ($user) {
            // Load pegawai relation
            $user->load('pegawai');

            // Store user data in session
            Session::put('user_id', $user->KODE);
            Session::put('username', $user->USERNAME);
            Session::put('nama_lengkap', $user->nama_lengkap);
            Session::put('pegawai_id', $user->PEGAWAI);
            Session::put('user_hak', $user->HAK);
            Session::put('is_logged_in', true);

            return response()->json([
                'success' => true,
                'message' => 'Login berhasil',
                'redirect' => route('dashboard')
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Username atau password salah'
            ], 401);
        }
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request)
    {
        Session::flush();
        return redirect()->route('welcome')->with('message', 'Logout berhasil');
    }

    /**
     * Check if user is authenticated
     */
    public function checkAuth()
    {
        if (Session::get('is_logged_in')) {
            return response()->json([
                'authenticated' => true,
                'user' => [
                    'id' => Session::get('user_id'),
                    'username' => Session::get('username'),
                    'pegawai_id' => Session::get('pegawai_id'),
                    'hak' => Session::get('user_hak')
                ]
            ]);
        }

        return response()->json([
            'authenticated' => false
        ]);
    }
}
