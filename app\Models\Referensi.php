<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;

class Referensi
{
    public static function getReferensi($jenis, $status = 1, $id = null, $exclude = [])
    {
        $query = DB::table('master.referensi')
            ->select([
                'jenis as jenis',
                'id as id',
                'deskripsi as deskripsi',
                'mapping_form as mapping_form',
                'status as status'
            ])
            ->where('status', $status)
            ->where('jenis', $jenis);

        if (!empty($exclude)) {
            $query->whereNotIn('id', $exclude);
        }

        if (!is_null($id)) {
            $query->where('id', $id);
        }

        return $query->get();
    }

    public static function getAsuransiList($id_asuransi = null)
    {
        $not_in = [1, 2, 4, 40, 826];
        return self::getReferensi(10, 1, $id_asuransi, $not_in);
    }
}
