<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Pasien_new;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PasienController extends Controller
{
    public function index(Request $request)
    {
        // Validasi manual parameter nomr (wajib, integer)
        $nomr = $request->query('nomr');
        if (is_null($nomr) || !is_numeric($nomr)) {
            return response()->json([
                'success' => false,
                'message' => 'Parameter nomr wajib diisi dan berupa angka'
            ], 400);
        }

        // Ambil parameter lain (opsional)
        $nik = $request->query('nik');
        $no_wa = $request->query('no_wa');
        $tgl_lahir = $request->query('tgl_lahir');

        // Validasi tgl_lahir jika diisi, harus format tanggal
        if (!is_null($tgl_lahir) && strtotime($tgl_lahir) === false) {
            return response()->json([
                'success' => false,
                'message' => 'Format tgl_lahir tidak valid'
            ], 400);
        }

        // Siapkan parameter untuk model
        $params = [
            'nomr' => $nomr,
            'nik' => $nik,
            'no_wa' => $no_wa,
            'tgl_lahir' => $tgl_lahir
        ];

        // Panggil fungsi model untuk mendapatkan data pasien
        $data = Pasien_new::getPatientData($nomr, $params);

        // Jika data kosong, kirim respon
        if (empty($data)) {
            return response()->json([
                'success' => true,
                'message' => 'Data pasien tidak ditemukan',
                'data' => []
            ]);
        }

        // Jika data ditemukan
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    public function register(Request $request)
    {
        // Field wajib selalu
        $fields = [
            'nomr', 'pilih_dokter', 'id_ruangan', 'jadwal_dokter', 'jenis_jaminan', 'rencana'
        ];
        $data = $request->only(array_merge($fields, ['instansi_asuransi', 'no_insurance']));

        $missing = [];
        foreach ($fields as $field) {
            if (empty($data[$field]) && $data[$field] !== "0") {
                $missing[] = $field;
            }
        }

        // Jika jenis_jaminan = 97, instansi_asuransi dan no_insurance wajib diisi
        if ($data['jenis_jaminan'] == 97) {
            if (empty($data['instansi_asuransi']) && $data['instansi_asuransi'] !== "0") {
                $missing[] = 'instansi_asuransi';
            }
            if (empty($data['no_insurance']) && $data['no_insurance'] !== "0") {
                $missing[] = 'no_insurance';
            }
        }

        if (!empty($missing)) {
            return response()->json([
                'success' => false,
                'message' => 'Field wajib: ' . implode(', ', $missing)
            ], 422);
        }

        $nomr = $data['nomr'];
        $dataPasien = Pasien_new::getPatientData($nomr);

        if (!$dataPasien) {
            return response()->json([
                'success' => false,
                'message' => 'Data pasien tidak ditemukan'
            ]);
        }

        $jaminan = $data['jenis_jaminan'];
        $insurance_id = $data['instansi_asuransi'] ?? null;
        if ($jaminan == 95) {
            $jaminan_id = 2;
        } else if ($jaminan == 97) {
            $jaminan_id = $insurance_id;
        } else {
            $jaminan_id = 1;
        }

        // Cek apakah sudah ada perjanjian dengan data yang sama dan status 1
        $existing = Pasien_new::checkExistingPerjanjian(
            $nomr,
            $data['pilih_dokter'],
            $data['id_ruangan'],
            $data['jadwal_dokter']
        );

        if ($existing) {
            return response()->json([
                'success' => false,
                'message' => 'Sudah ada perjanjian dengan data yang sama dan status aktif.'
            ], 409);
        }

        $dataperjanjian = [
            'NOMR' => $nomr,
            'NAMAPASIEN' => $dataPasien->nama_pasien ?? "",
            'ID_DOKTER' => $data['pilih_dokter'],
            'ID_RUANGAN' => $data['id_ruangan'],
            'TANGGAL' => $data['jadwal_dokter'],
            'KETERANGAN' => 0,
            'RENCANA' => $data['rencana'],
            'NOMOR' => $dataPasien->no_whatsapp ?? "",
            'OLEH' => 0,
            'STATUS' => 1,
            'JAMINAN' => $jaminan_id,
            'NO_JAMINAN' => $data['no_insurance'] ?? ""
        ];

        try {
            Pasien_new::insertPerjanjian($dataperjanjian);
            return response()->json([
                'success' => true,
                'message' => 'Perjanjian berhasil didaftarkan'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mendaftar perjanjian',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
