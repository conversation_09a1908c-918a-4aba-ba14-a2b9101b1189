<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Traits\LoggingTrait;

class Pasien extends Model
{
    use LoggingTrait;

    protected $table = 'master.pasien';

    public static function getPasienData($norm, $date_procedure, $nip = null)
    {
        $query = "
        SELECT
            pp.NORM AS medical_number,
            mpas.NAMA AS nama_pasien,
            CASE
                WHEN mpas.JENIS_KELAMIN = 1 THEN 'L'
                ELSE 'P'
            END AS sex,
            DATE(mpas.TANGGAL_LAHIR) AS tgl_lahir,
            COALESCE(mmed.desk_diagnosa_medis, ktc.analisis) AS diagnosis,
            ltm.TANGGAL AS date_procedure,
            mt.NAMA AS `procedure`,
            mr1.DESKRIPSI AS room,
            (
                SELECT
                    GROUP_CONCAT(COALESCE(sub_mmed.desk_diagnosa_medis, tc.analisis) SEPARATOR ', ') 
                FROM
                    pendaftaran.pendaftaran sub_pp
                    LEFT JOIN medis.tb_medis sub_med ON sub_med.nopen = sub_pp.NOMOR
                    LEFT JOIN medis.tb_masalah_medis_kep sub_mmed ON sub_mmed.id_emr = sub_med.id_emr
                    LEFT JOIN pendaftaran.kunjungan sub_pk ON sub_pk.NOPEN = sub_pp.NOMOR
                    LEFT JOIN keperawatan.tb_cppt tc ON tc.nokun = sub_pk.NOMOR 
                WHERE
                    sub_pp.NORM = pp.NORM 
                    AND (sub_med.created_at < pp.TANGGAL OR tc.nokun IS NOT NULL)
                ORDER BY
                    COALESCE(sub_med.created_at, tc.tanggal) DESC 
            ) AS patient_history 
        FROM
            pendaftaran.pendaftaran pp
        LEFT JOIN pendaftaran.kunjungan pk ON pp.NOMOR = pk.NOPEN
        LEFT JOIN medis.tb_medis med ON med.nopen = pp.NOMOR AND med.flag = 1
        LEFT JOIN medis.tb_masalah_medis_kep mmed ON mmed.id_emr = med.id_emr
        LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = pp.NOMOR
        LEFT JOIN `master`.ruangan mr1 ON mr1.ID = tp.RUANGAN
        LEFT JOIN `master`.ruangan mr2 ON mr2.ID = pk.RUANGAN
        LEFT JOIN `master`.dokter md ON md.ID = tp.DOKTER
        LEFT JOIN `master`.pegawai mp ON mp.NIP = md.NIP
        LEFT JOIN `master`.pasien mpas ON mpas.NORM = pp.NORM
        LEFT JOIN layanan.tindakan_medis ltm ON ltm.KUNJUNGAN = pk.NOMOR
        LEFT JOIN `master`.tindakan mt ON mt.ID = ltm.TINDAKAN
        LEFT JOIN keperawatan.tb_cppt ktc ON ktc.nokun = pk.NOMOR 
        WHERE
            pp.NORM = ?
            AND mp.PROFESI = 11
            AND mr1.ID = '105120101'
        ";
    
        $params = [$norm];
    
        if ($date_procedure) {
            $query .= " AND DATE(pp.TANGGAL) = ?";
            $params[] = $date_procedure;
        }
    
        if ($nip) {
            $query .= " AND md.NIP = ?";
            $params[] = $nip;
        }
    
        $query .= " ORDER BY pp.TANGGAL DESC LIMIT 1";
    
        // Generate the interpolated query for debugging
        $interpolatedQuery = self::interpolateQuery($query, $params);
        static::$lastQuery = $interpolatedQuery;
    
        try {
            $results = DB::select($query, $params);
            self::logApi($results, null, 'success', 200);
            return $results;
        } catch (\Exception $e) {
            self::logApi(null, $e, 'error', 500);
            throw $e;
        }
    }


    // Static property to store the last executed query
    public static $lastQuery = null;

    // Helper function to interpolate query parameters
    protected static function interpolateQuery($query, $params)
    {
        $keys = array();
        $values = $params;

        // Build a regular expression for each parameter
        foreach ($params as $key => $value) {
            if (is_string($value)) {
                $values[$key] = "'" . $value . "'";
            }
            $keys[] = '/\?/';
        }

        $query = preg_replace($keys, $values, $query, 1, $count);

        // Clean up the query by removing \r\n and excessive whitespace
        $query = preg_replace('/\s+/', ' ', $query);
        $query = trim($query);

        return $query;
    }
}
