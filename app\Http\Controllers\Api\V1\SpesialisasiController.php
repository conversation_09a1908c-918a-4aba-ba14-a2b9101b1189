<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Referensi;

class SpesialisasiController extends Controller
{
    public function index(Request $request)
    {
        $id_spesialisasi = $request->query('id_spesialisasi');
        
        // Validasi bahwa jika id_spesialisasi diberikan, harus berupa angka
        if (!is_null($id_spesialisasi) && !is_numeric($id_spesialisasi)) {
            return response()->json([
                'success' => false,
                'message' => 'ID spesialisasi tidak valid',
                'data' => []
            ], 400);
        }

        $data = Referensi::getReferensi(26, 1, $id_spesialisasi);

        // Gunakan empty() agar aman untuk berbagai tipe data
        if (empty($data)) {
            return response()->json([
                'success' => true,
                'message' => 'Data spesialisasi tidak ditemukan',
                'data' => []
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Data spesialisasi ditemukan',
            'data' => $data
        ]);
    }
}
