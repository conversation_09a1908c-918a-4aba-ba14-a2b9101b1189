<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\UserApi; // Model untuk tabel aplikasi.user_api

class AuthController extends Controller
{
    public function index(Request $request)
    {
        // Validasi input
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        // Cari user berdasarkan username dan status aktif
        $user = UserApi::where('username', $request->username)
                       ->where('status', 1)
                       ->first();

        // Periksa apakah user ditemukan dan password cocok
        if ($user && Hash::check($request->password, $user->password)) {
            // Generate token bearer
            $token = Str::random(60);

            // Simpan token di cache (berkedaluwarsa dalam 30 menit)
            Cache::put($token, ['username' => $user->username], now()->addMinutes(30));

            // Output JSON dengan token
            return response()->json([
                'success' => true,
                'code' => 200,
                'access_token' => $token,
                'token_type' => 'Bearer',
                'expires_in' => 30, // menit
                'message' => 'Success',
            ], 200);
        }

        // Jika username atau password salah
        return response()->json([
            'success' => false,
            'code' => 401,
            'message' => 'Invalid username or password',
        ], 401);
    }
}
