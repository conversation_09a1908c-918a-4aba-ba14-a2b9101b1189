<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;

class Ruangan
{
    public static function getRuanganPoli($id_ruangan = null)
    {
        $query = DB::table('master.ruangan')
            ->select([
                'id as id',
                'jenis as jenis',
                'jenis_kunjungan as jenis_kunjungan',
                'deskripsi as deskripsi',
                'lantai as lantai',
                'antrian as antrian',
                'gedung as gedung',
                'status as status'
            ])
            ->where('jenis_kunjungan', 1)
            ->where('status',1);

        if ($id_ruangan) {
            $query->where('ID', $id_ruangan);
        }

        return $query->get();
    }
}