<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Pasien;
use Illuminate\Validation\ValidationException;

class ApiPasien extends Controller
{
    public function index(Request $request)
    {
        $medical_number = $request->input('medical_number');
        $date_procedure = $request->input('date_procedure');
        $nip = $request->input('nip');

        $errors = [];
        if (empty($medical_number)) {
            $errors['medical_number'] = ['The medical number field is required.'];
        }
        if (empty($date_procedure)) {
            $errors['date_procedure'] = ['The date procedure field is required.'];
        } else if (!strtotime($date_procedure)) {
            $errors['date_procedure'] = ['The date procedure is not a valid date.'];
        }

        if (!empty($errors)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $errors
            ], 422);
        }

        try {
            $results = Pasien::getPasienData($medical_number, $date_procedure, $nip);

            if (!empty($results)) {
                $patientData = $results[0];
                return response()->json([
                    'success' => 'true',
                    'code' => '200',
                    'status' => 'success',
                    'medical_number' => $patientData->medical_number,
                    'nama_pasien' => $patientData->nama_pasien,
                    'sex' => $patientData->sex,
                    'tgl_lahir' => $patientData->tgl_lahir,
                    'diagnosis' => $patientData->diagnosis,
                    'date_procedure' => $patientData->date_procedure,
                    'procedure' => $patientData->procedure,
                    'room' => $patientData->room,
                    'patient_history' => $patientData->patient_history,
                    // 'query' => Pasien::$lastQuery, 
                ]);
            }

            return response()->json([
                'status' => 'error',
                'description' => 'Data Not Found',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'An unexpected error occurred'
            ], 500);
        }
    }
}
