<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthWebController;
use App\Http\Controllers\PresensiController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
})->name('welcome');

// Authentication routes
Route::post('/login', [AuthWebController::class, 'login'])->name('login');
Route::post('/logout', [AuthWebController::class, 'logout'])->name('logout');
Route::get('/check-auth', [AuthWebController::class, 'checkAuth'])->name('check.auth');

// Debug route (remove in production)
Route::get('/debug-user/{username}', function($username) {
    try {
        $user = \App\Models\AccountAuth::where('USERNAME', $username)->first();
        if ($user) {
            return response()->json([
                'found' => true,
                'username' => $user->USERNAME,
                'password' => $user->PASSWORD,
                'password_length' => strlen($user->PASSWORD),
                'password_starts_with' => substr($user->PASSWORD, 0, 10),
                'status' => $user->STATUS,
                'pegawai' => $user->PEGAWAI,
                'hak' => $user->HAK,
                'is_bcrypt' => substr($user->PASSWORD, 0, 4) === '$2y$'
            ]);
        }
        return response()->json(['found' => false, 'message' => 'User not found']);
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()]);
    }
});

// Test login route
Route::get('/test-login/{username}/{password}', function($username, $password) {
    try {
        $result = \App\Models\AccountAuth::authenticate($username, $password);
        if ($result) {
            return response()->json(['success' => true, 'user' => $result->USERNAME]);
        }
        return response()->json(['success' => false, 'message' => 'Authentication failed']);
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()]);
    }
});

// Dashboard and Presensi routes (protected)
Route::middleware('auth.web')->group(function () {
    Route::get('/dashboard', [PresensiController::class, 'dashboard'])->name('dashboard');
    Route::post('/presensi/masuk', [PresensiController::class, 'checkIn'])->name('presensi.masuk');
    Route::post('/presensi/keluar', [PresensiController::class, 'checkOut'])->name('presensi.keluar');
    Route::get('/presensi/status', [PresensiController::class, 'getAttendanceStatus'])->name('presensi.status');
    Route::get('/presensi/detail/{id}', [PresensiController::class, 'getAttendanceDetail'])->name('presensi.detail');
});
