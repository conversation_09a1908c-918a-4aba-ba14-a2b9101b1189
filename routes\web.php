<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthWebController;
use App\Http\Controllers\PresensiController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
})->name('welcome');

// Authentication routes
Route::post('/login', [AuthWebController::class, 'login'])->name('login');
Route::post('/logout', [AuthWebController::class, 'logout'])->name('logout');
Route::get('/check-auth', [AuthWebController::class, 'checkAuth'])->name('check.auth');

// Dashboard and Presensi routes (protected)
Route::middleware('auth.web')->group(function () {
    Route::get('/dashboard', [PresensiController::class, 'dashboard'])->name('dashboard');
    Route::post('/presensi/masuk', [PresensiController::class, 'checkIn'])->name('presensi.masuk');
    Route::post('/presensi/keluar', [PresensiController::class, 'checkOut'])->name('presensi.keluar');
    Route::get('/presensi/status', [PresensiController::class, 'getAttendanceStatus'])->name('presensi.status');
    Route::get('/presensi/detail/{id}', [PresensiController::class, 'getAttendanceDetail'])->name('presensi.detail');
});
