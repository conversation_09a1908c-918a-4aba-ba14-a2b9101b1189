# Memory Bank - PPDS RSPPU Project

## Project Overview
Sistem login dan dashboard presensi untuk PPDS (Program Pendidikan Dokter Spesialis) RSPPU RS Kanker Dharmais dengan fitur:
- Login menggunakan database terpisah (dbsdm)
- Dashboard dengan presensi menggunakan kamera dan geolocation
- Penyimpanan data presensi ke database ppds

## Database Architecture

### Authentication Database (dbsdm)
- **Host**: *************
- **Database**: dbsdm
- **User**: simpeg
- **Password**: 5!Mp36^2024
- **Table**: account
  - KODE (int, 11) - Primary Key
  - PEGAWAI (int, 11) - Foreign Key to pegawai1.NIP
  - USERNAME (varchar)
  - PASSWORD (varchar, 100)
  - HAK (tinyint, 1)
  - STATUS (char, 1)
  - TANGGAL (datetime)
  - OLEH (int, 11)

### PPDS Database (ppds)
- **Host**: *************
- **Database**: ppds
- **User**: simpeg
- **Password**: 5!Mp36^2024
- **Table**: presensi_ppds
  - id (Primary Key)
  - user_id, username, nama_lengkap
  - tanggal, jam_masuk, jam_keluar
  - foto_masuk, foto_keluar
  - latitude_masuk, longitude_masuk, latitude_keluar, longitude_keluar
  - alamat_masuk, alamat_keluar
  - status, keterangan
  - timestamps

## Key Models

### AccountAuth
- Connection: auth_db
- Table: account
- Primary Key: KODE
- Relations: belongsTo PegawaiAuth
- Methods: authenticate(), findByUsername(), getNamaLengkapAttribute()

### PresensiPpds
- Connection: ppds_db
- Table: presensi_ppds
- Methods: checkIn(), checkOut(), hasAttendanceToday(), getTodayAttendance()

### PegawaiAuth
- Connection: auth_db
- Table: pegawai1
- Primary Key: NIP
- Methods: findByNip(), getNamaLengkap()

## Controllers

### AuthWebController
- login(): Handle login dengan session
- logout(): Clear session dan redirect
- checkAuth(): Check authentication status

### PresensiController
- dashboard(): Show dashboard dengan data presensi
- checkIn(): Handle presensi masuk dengan foto dan lokasi
- checkOut(): Handle presensi keluar dengan foto dan lokasi
- getAttendanceStatus(): Get status presensi hari ini

## Frontend Features

### Welcome Page
- Modal login dengan Bootstrap
- AJAX login dengan CSRF protection
- Redirect ke dashboard setelah login berhasil

### Dashboard
- Modern UI dengan sidebar navigation
- Camera access untuk foto presensi
- Geolocation tracking
- Real-time attendance status
- Attendance history table
- Responsive design

### JavaScript Features
- Camera API integration
- Geolocation API
- Base64 image capture
- AJAX requests untuk presensi
- Loading modals
- Error handling

## Security Features
- CSRF protection
- Session-based authentication
- Middleware protection untuk routes
- Input validation
- SQL injection protection via Eloquent

## File Storage
- Photos stored in: storage/app/public/presensi/
- Naming convention: presensi_{type}_{user_id}_{timestamp}.jpg
- Public access via storage link

## Routes Structure
- GET / : Welcome page
- POST /login : Authentication
- POST /logout : Logout
- GET /dashboard : Dashboard (protected)
- POST /presensi/masuk : Check-in (protected)
- POST /presensi/keluar : Check-out (protected)
- GET /presensi/status : Get attendance status (protected)

## Development Notes
- Laravel 8.x framework
- Bootstrap 5 for UI
- MySQL databases with separate connections
- Photo storage using Laravel Storage
- Session-based authentication (not Laravel Auth)
- Middleware for route protection
- AOS library for animations
- Bootstrap Icons for UI elements

## Recent Improvements
- Enhanced error handling with try-catch blocks
- Photo validation (size, format)
- Geolocation with detailed error messages
- Toast notifications for better UX
- Loading states for button actions
- Detail view modal for attendance history
- API endpoint for attendance details
- Improved JavaScript with validation
- Better camera handling and photo capture
- Responsive design optimizations

## Testing Status
- ✅ Server running on http://0.0.0.0:8000
- ✅ Database migrations completed
- ✅ Storage link created
- ✅ All routes configured
- ✅ Models and controllers implemented
- ✅ Frontend JavaScript functional
- ✅ Error handling implemented
- ✅ Security features active

## Deployment Considerations
- Ensure database connections are accessible
- Configure storage permissions
- Set proper CSRF token handling
- Configure session settings
- Set up proper error logging
- Consider HTTPS for production (required for camera)
- Optimize images and assets
- Test camera permissions on production
- Verify geolocation works on target devices
