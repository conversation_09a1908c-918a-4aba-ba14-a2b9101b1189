<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PresensiPpds;
use App\Models\AccountAuth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class PresensiController extends Controller
{
    /**
     * Show dashboard page
     */
    public function dashboard()
    {
        if (!Session::get('is_logged_in')) {
            return redirect()->route('welcome');
        }

        $userId = Session::get('user_id');
        $username = Session::get('username');
        
        // Get user details
        $user = AccountAuth::with('pegawai')->find($userId);
        
        // Get today's attendance
        $todayAttendance = PresensiPpds::getTodayAttendance($userId);
        
        // Get attendance history
        $attendanceHistory = PresensiPpds::getAttendanceHistory($userId, 5);

        return view('dashboard', compact('user', 'todayAttendance', 'attendanceHistory'));
    }

    /**
     * Handle check-in
     */
    public function checkIn(Request $request)
    {
        if (!Session::get('is_logged_in')) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
        }

        $request->validate([
            'foto' => 'required|string', // base64 image
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'alamat' => 'nullable|string'
        ]);

        $userId = Session::get('user_id');
        $username = Session::get('username');

        // Check if already checked in today
        if (PresensiPpds::hasAttendanceToday($userId)) {
            return response()->json([
                'success' => false,
                'message' => 'Anda sudah melakukan presensi hari ini'
            ]);
        }

        // Get user details
        $user = AccountAuth::with('pegawai')->find($userId);

        try {
            // Save photo
            $photoName = $this->savePhoto($request->foto, 'masuk', $userId);

            // Create attendance record
            $attendance = PresensiPpds::checkIn([
                'user_id' => $userId,
                'username' => $username,
                'nama_lengkap' => $user->nama_lengkap,
                'foto' => $photoName,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'alamat' => $request->alamat
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Presensi masuk berhasil dicatat',
                'data' => $attendance
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menyimpan presensi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle check-out
     */
    public function checkOut(Request $request)
    {
        if (!Session::get('is_logged_in')) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
        }

        $request->validate([
            'foto' => 'required|string', // base64 image
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'alamat' => 'nullable|string'
        ]);

        $userId = Session::get('user_id');

        // Get today's attendance
        $attendance = PresensiPpds::getTodayAttendance($userId);

        if (!$attendance) {
            return response()->json([
                'success' => false,
                'message' => 'Anda belum melakukan presensi masuk hari ini'
            ]);
        }

        if ($attendance->jam_keluar) {
            return response()->json([
                'success' => false,
                'message' => 'Anda sudah melakukan presensi keluar hari ini'
            ]);
        }

        try {
            // Save photo
            $photoName = $this->savePhoto($request->foto, 'keluar', $userId);

            // Update attendance record
            $attendance->checkOut([
                'foto' => $photoName,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'alamat' => $request->alamat
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Presensi keluar berhasil dicatat',
                'data' => $attendance->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menyimpan presensi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Save photo from base64
     */
    private function savePhoto($base64Image, $type, $userId)
    {
        // Remove data:image/jpeg;base64, part
        $image = str_replace('data:image/jpeg;base64,', '', $base64Image);
        $image = str_replace(' ', '+', $image);
        $imageData = base64_decode($image);

        // Validate image size (max 2MB)
        $imageSize = strlen($imageData);
        if ($imageSize > 2 * 1024 * 1024) {
            throw new \Exception('Ukuran foto terlalu besar. Maksimal 2MB.');
        }

        // Validate if it's a valid image
        $imageInfo = getimagesizefromstring($imageData);
        if (!$imageInfo) {
            throw new \Exception('File yang diupload bukan gambar yang valid.');
        }

        // Generate filename
        $filename = 'presensi_' . $type . '_' . $userId . '_' . Carbon::now()->format('Y-m-d_H-i-s') . '.jpg';

        // Save to storage
        Storage::disk('public')->put('presensi/' . $filename, $imageData);

        return $filename;
    }

    /**
     * Get attendance status for today
     */
    public function getAttendanceStatus()
    {
        if (!Session::get('is_logged_in')) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
        }

        $userId = Session::get('user_id');
        $attendance = PresensiPpds::getTodayAttendance($userId);

        return response()->json([
            'success' => true,
            'data' => $attendance
        ]);
    }

    /**
     * Get attendance detail by ID
     */
    public function getAttendanceDetail($id)
    {
        if (!Session::get('is_logged_in')) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
        }

        $userId = Session::get('user_id');
        $attendance = PresensiPpds::where('id', $id)
                                  ->where('user_id', $userId)
                                  ->first();

        if (!$attendance) {
            return response()->json([
                'success' => false,
                'message' => 'Data presensi tidak ditemukan'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $attendance->id,
                'tanggal' => $attendance->tanggal->format('d/m/Y'),
                'jam_masuk' => $attendance->jam_masuk ? $attendance->jam_masuk->format('H:i:s') : null,
                'jam_keluar' => $attendance->jam_keluar ? $attendance->jam_keluar->format('H:i:s') : null,
                'foto_masuk' => $attendance->foto_masuk ? asset('storage/presensi/' . $attendance->foto_masuk) : null,
                'foto_keluar' => $attendance->foto_keluar ? asset('storage/presensi/' . $attendance->foto_keluar) : null,
                'lokasi_masuk' => $attendance->latitude_masuk && $attendance->longitude_masuk ?
                    $attendance->latitude_masuk . ', ' . $attendance->longitude_masuk : null,
                'lokasi_keluar' => $attendance->latitude_keluar && $attendance->longitude_keluar ?
                    $attendance->latitude_keluar . ', ' . $attendance->longitude_keluar : null,
                'alamat_masuk' => $attendance->alamat_masuk,
                'alamat_keluar' => $attendance->alamat_keluar,
                'status' => $attendance->status,
                'keterangan' => $attendance->keterangan
            ]
        ]);
    }
}
