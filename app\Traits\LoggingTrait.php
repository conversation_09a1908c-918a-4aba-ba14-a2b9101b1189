<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;

trait LoggingTrait
{
    /**
     * Log API request and response
     *
     * @param mixed $response
     * @param \Exception|null $exception
     * @param string $status
     * @param int $code
     * @return void
     */
    public static function logApi($response, \Exception $exception = null, string $status = 'success', int $code = 200)
    {
        $logData = [
            'ip' => request()->ip(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'response' => json_encode($exception ? [
                'error' => 'Database error',
                'message' => $exception->getMessage()
            ] : $response),
            'status' => $status,
            'code' => $code,
            'created_at' => now()
        ];

        DB::table('rsppu.log_api')->insert($logData);
    }
}