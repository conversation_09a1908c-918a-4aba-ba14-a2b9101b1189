<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ApiPasien;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ApiPpds;
use App\Http\Controllers\Api\V1\PasienController;
use App\Http\Controllers\Api\V1\AsuransiController;
use App\Http\Controllers\Api\V1\RuanganController;
use App\Http\Controllers\Api\V1\SpesialisasiController;
use App\Http\Controllers\Api\V1\DoctorController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::post('/v1/auth', [AuthController::class, 'index']);
Route::post('/v1/getMedicalRecord', [ApiPasien::class, 'index'])->middleware('auth.bearer');
Route::get('/v1/getAbsensi', [ApiPpds::class, 'getAbsensi'])->middleware('auth.bearer');
Route::get('/v1/getPresensi', [ApiPpds::class, 'getPresensi'])->middleware('auth.bearer');


Route::middleware('auth.bearer')->group(function () {
    Route::group(['prefix' => 'v1'], function () {
        Route::get('pasien', [PasienController::class, 'index']);
        Route::get('asuransi', [AsuransiController::class, 'list']);
        Route::get('ruangan', [RuanganController::class, 'index']);
        Route::get('spesialisasi', [SpesialisasiController::class, 'index']);
        Route::get('dokter', [DoctorController::class, 'index']);
        Route::post('pasien/register', [PasienController::class, 'register']);
    });
});